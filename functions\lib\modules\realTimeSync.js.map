{"version": 3, "file": "realTimeSync.js", "sourceRoot": "", "sources": ["../../src/modules/realTimeSync.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iEAAmD;AACnD,sDAAwC;AAExC;;;;GAIG;AAEH,+BAA+B;AAC/B,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAC1C,MAAM,mBAAmB,GAAG,WAAW,CAAC;AACxC,MAAM,gBAAgB,GAAG,OAAO,CAAC;AACjC,MAAM,qBAAqB,GAAG,YAAY,CAAC;AAE3C;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,UAAU,CACvE,KAAK,EAAE,MAAM,EAAE,EAAE;IACf,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;YAC9B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW;SAChC,CAAC,CAAC;QAEH,6CAA6C;QAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,IAAc,CAAC;QACzC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,UAAU,CAAC;QAC5B,MAAM,QAAQ,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,KAAI,cAAc,CAAC;QAC9D,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,0BAA0B,CAAC;QAErE,sCAAsC;QACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACxC,OAAO;QACT,CAAC;QAED,sEAAsE;QACtE,0FAA0F;QAC1F,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAE5E,MAAM,cAAc,GAAG,MAAM,gCAAgC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC5F,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE,QAAQ,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,cAAc,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,QAAQ,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAChF,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,+CAA+C;QAC/C,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAE3E,oEAAoE;QACpE,wCAAwC;QAExC,2DAA2D;QAC3D,MAAM,yBAAyB,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAEjE,4DAA4D;QAC5D,wCAAwC;IAC1C,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;;GAGG;AACU,QAAA,oBAAoB,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,QAAQ,CACrE,KAAK,EAAE,MAAM,EAAE,EAAE;IACf,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAE7C,MAAM,UAAU,GAAG,MAAM,CAAC,IAAc,CAAC;QACzC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,UAAU,CAAC;QAE5B,qCAAqC;QACrC,MAAM,aAAa,GAAG,MAAM,KAAK;aAC9B,SAAS,EAAE;aACX,UAAU,CAAC,mBAAmB,CAAC;aAC/B,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;aAC7B,GAAG,EAAE,CAAC;QAET,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,QAAQ,CAAC,CAAC;YACvE,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC;QACxC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACjC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,aAAa,aAAa,CAAC,IAAI,CAAC,MAAM,YAAY,CAAC,CAAC;QAEhE,oEAAoE;QACpE,wCAAwC;QAExC,8BAA8B;QAC9B,MAAM,yBAAyB,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACpD,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAEjE,4DAA4D;QAC5D,wCAAwC;IAC1C,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;;GAGG;AACU,QAAA,iBAAiB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAC7D,KAAK,EAAE,IAAI,EAAE,EAAE;;IACb,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;YAC9B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;SACzC,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,YAAY,GAAG,MAAM,KAAK;aAC7B,SAAS,EAAE;aACX,UAAU,CAAC,gBAAgB,CAAC;aAC5B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;aACb,GAAG,EAAE,CAAC;QAET,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO;QACT,CAAC;QAED,mDAAmD;QACnD,MAAM,WAAW,GAAG,MAAM,KAAK;aAC5B,SAAS,EAAE;aACX,UAAU,CAAC,gBAAgB,CAAC;aAC5B,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;aAChC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,gBAAgB;aACtE,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,qDAAqD,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/E,OAAO;QACT,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG;YAClB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW,KAAI,MAAA,IAAI,CAAC,KAAK,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA,IAAI,MAAM;YACpE,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;YAC7B,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,MAAM,EAAE,eAAe;YAC7B,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACvD,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACzD,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACvD,CAAC;QAEF,MAAM,KAAK;aACR,SAAS,EAAE;aACX,UAAU,CAAC,gBAAgB,CAAC;aAC5B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;aACb,GAAG,CAAC,WAAW,CAAC,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAE1D,oEAAoE;QACpE,wCAAwC;QAExC,8BAA8B;QAC9B,MAAM,yBAAyB,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAE9D,4DAA4D;QAC5D,wCAAwC;IAC1C,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;;GAGG;AACU,QAAA,iBAAiB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAC7D,KAAK,EAAE,IAAI,EAAE,EAAE;IACb,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAE1C,4CAA4C;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;YAEvB,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC;QAED,oEAAoE;QACpE,wCAAwC;QAExC,8BAA8B;QAC9B,MAAM,yBAAyB,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAE9D,4DAA4D;QAC5D,wCAAwC;IAC1C,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AAEH,sCAAsC;AACtC,KAAK,UAAU,gCAAgC,CAC7C,QAAgB,EAChB,QAAgB,EAChB,QAAgB;IAEhB,IAAI,CAAC;QACH,iCAAiC;QACjC,MAAM,SAAS,GAAG,MAAM,KAAK;aAC1B,SAAS,EAAE;aACX,UAAU,CAAC,mBAAmB,CAAC;aAC/B,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC;aACR,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO;gBACL,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE;oBACP,MAAM,EAAE,kBAAkB;oBAC1B,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;iBACtC;aACF,CAAC;QACJ,CAAC;QAED,mCAAmC;QACnC,MAAM,SAAS,GAAG,MAAM,KAAK;aAC1B,SAAS,EAAE;aACX,UAAU,CAAC,mBAAmB,CAAC;aAC/B,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC;aACR,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO;gBACL,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE;oBACP,MAAM,EAAE,qBAAqB;oBAC7B,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;iBACtC;aACF,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;IAC9G,CAAC;AACH,CAAC;AAED,kEAAkE;AAClE,KAAK,UAAU,wBAAwB,CAAC,QAAgB;IACtD,MAAM,aAAa,GAAG,MAAM,KAAK;SAC9B,SAAS,EAAE;SACX,UAAU,CAAC,mBAAmB,CAAC;SAC/B,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;SACjC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;SAC7B,KAAK,CAAC,CAAC,CAAC;SACR,GAAG,EAAE,CAAC;IAET,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9B,CAAC;AAED,iDAAiD;AACjD,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,oDAAoD;IACpD,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED,sCAAsC;AACtC,SAAS,0BAA0B,CAAC,WAAmB;IACrD,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;QAAE,OAAO,OAAO,CAAC;IACrD,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAC9C,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC;QAAE,OAAO,UAAU,CAAC;IACxF,IAAI,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;QAAE,OAAO,aAAa,CAAC;IAC/F,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;QAAE,OAAO,cAAc,CAAC;IACtG,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,kCAAkC;AAClC,SAAS,uBAAuB,CAAC,QAAgB;IAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;IACrD,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,kDAAkD;AAClD,2FAA2F;AAC3F,6EAA6E;AAE7E,2DAA2D;AAC3D,KAAK,UAAU,yBAAyB;IACtC,IAAI,CAAC;QACH,MAAM,KAAK;aACR,SAAS,EAAE;aACX,UAAU,CAAC,kBAAkB,CAAC;aAC9B,GAAG,CAAC,cAAc,CAAC;aACnB,MAAM,EAAE,CAAC;QAEZ,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;AACH,CAAC"}